{"name": "@vitejs/plugin-vue", "version": "3.2.0", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-vue"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-vue#readme", "peerDependencies": {"vite": "^3.0.0", "vue": "^3.2.25"}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "debug": "^4.3.4", "rollup": "^2.79.1", "slash": "^5.0.0", "source-map": "^0.6.1", "vite": "workspace:*", "vue": "^3.2.41"}}