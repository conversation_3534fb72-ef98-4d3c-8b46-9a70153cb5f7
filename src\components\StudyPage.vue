<template>
  <div>
    <h1>Study Time Tracker</h1>
    <p>Total Study Time: {{ totalSeconds }} seconds</p>
    <iframe ref="learningFrame" src="https://www.youtube.com/embed/videoseries?list=PL4cUxeGkcC9g_2d-2J54-h2B9gQjLgH4_" width="560" height="315" frameborder="0" allowfullscreen></iframe>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

const totalSeconds = ref(0)
const isTabActive = ref(true)
let timer = null

const startTimer = () => {
  timer = setInterval(() => {
    if (isTabActive.value) {
      totalSeconds.value++
    }
  }, 1000)
}

const stopTimer = () => {
  clearInterval(timer)
  timer = null
}

const handleVisibilityChange = () => {
  isTabActive.value = document.visibilityState === 'visible'
}

onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
  startTimer()
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  stopTimer()
  console.log('Total study time (seconds):', totalSeconds.value)
})

onBeforeRouteLeave((to, from, next) => {
  stopTimer()
  console.log('Router navigation, stopping timer. Total study time:', totalSeconds.value, 'seconds')
  next()
})
</script>