### 🎯 목적

* Iframe 학습 콘텐츠 접근 시 **학습 시작 시간** 기록
* 다른 페이지로 이동하거나 탭이 비활성화될 경우 **학습 종료 시간** 처리
* **브라우저 탭이 활성화된 시간만** 실제 학습 시간으로 누적 계산
* 타이머 기반으로 실시간 누적 시간 측정

---

### 🔧 구성

| 항목          | 설명                           |
| ----------- | ---------------------------- |
| 시작 시점       | iframe 로딩 또는 접근 이벤트 시 기록     |
| 종료 시점       | 페이지 이탈, 라우터 변경, 탭 비활성화 등     |
| 학습 시간 계산 방식 | 타이머로 1초 단위 카운트, 활성 탭일 경우만 누적 |
| Vue3 환경     | Composition API 기반 구성        |
| 타겟 iframe   | `ref="learningFrame"` 식별자 사용 |

---

### 🧩 주요 코드 샘플 (Composition API 기반)

```js
<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'

const totalSeconds = ref(0)
const isTabActive = ref(true)
let timer = null

const startTimer = () => {
  timer = setInterval(() => {
    if (isTabActive.value) {
      totalSeconds.value++
    }
  }, 1000)
}

const stopTimer = () => {
  clearInterval(timer)
  timer = null
}

const handleVisibilityChange = () => {
  isTabActive.value = document.visibilityState === 'visible'
}

onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
  startTimer()
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  stopTimer()
  console.log('총 학습 시간(초):', totalSeconds.value)
})

// 라우터 이동 감지 → 학습 종료 처리
onBeforeRouteLeave((to, from, next) => {
  stopTimer()
  console.log('라우터 이동으로 학습 종료:', totalSeconds.value, '초')
  next()
})
</script>
```

---

### 📌 참고 사항

* 학습 시간은 `totalSeconds`에 누적되며, 서버 전송 시 `startTime`, `endTime`, `elapsedTime`(초)로 변환하여 전송 가능
* 종료 시점에서는 IndexedDB나 LocalStorage 또는 API를 통해 이력을 저장 가능
* 탭 비활성화 상태에서는 타이머는 동작하지만 카운팅이 되지 않음 (`visibilityState` 활용)

---

### ✅ 기대 효과

* 실제 학습 시간 기반 로그 데이터 생성 가능
* 비정상 종료나 이탈 시에도 학습 시간 누락 최소화
* 추후 서버 연동 시 LRS 또는 사용자 로그와 정합성 유지 가능

